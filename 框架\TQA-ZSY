ATR周期:=14;
支撑波动系数:=1.5;
阻力波动系数:=0.8;
支撑基础系数:=1.0;
阻力基础系数:=1.2;

ATR:=EMA(MAX(MAX(H-L,ABS(H-REF(C,1))),ABS(L-REF(C,1))),ATR周期);
当前波动率:=ATR/C*100;
历史均值:=MA(当前波动率,ATR周期);
波动率倍数:=当前波动率/历史均值;

支撑动态周期:=ROUND(ATR周期*支撑基础系数/(POW(波动率倍数,1.5)*支撑波动系数));
阻力动态周期:=ROUND(ATR周期*阻力基础系数/(POW(波动率倍数,1.5)*阻力波动系数));

X1:=阻力动态周期;
X2:=支撑动态周期;

周期高点:REF(HHV(H,X1),1);
周期低点:REF(LLV(L,X2),1);
平空开多:=HIGH>=周期高点;
平多开空:=LOW<=周期低点;
BUYSHORT_BUY(平空开多,LOW);
SELL_SELLSHORT(平多开空,HIGH);
AUTOFILTER;

改进效果：
- 去掉标准化和平滑处理，直接使用原始波动率
- 使用1.5次方放大波动率影响
- 波动率是历史均值2倍时，支撑周期收缩为原来的1/(2^1.5×1.5) ≈ 1/4.2
- 效果更加明显和迅速