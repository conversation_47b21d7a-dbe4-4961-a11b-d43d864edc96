#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动止盈方案测试
测试基于14日ATR的移动止盈机制
"""

import numpy as np
import sys
import os

# 添加框架目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '框架'))

from 框架.sk线 import CMFBIASDivergenceDetector

def test_moving_profit_control():
    """测试移动止盈控制函数"""
    print("🧪 测试移动止盈控制函数")
    
    # 创建检测器实例
    detector = CMFBIASDivergenceDetector()
    
    # 模拟价格数据（20天）
    np.random.seed(42)
    base_price = 100.0
    
    # 生成模拟的OHLC数据
    closes = []
    highs = []
    lows = []
    
    for i in range(20):
        # 模拟价格波动
        daily_change = np.random.normal(0, 0.02)  # 2%的日波动
        price = base_price * (1 + daily_change)
        
        # 生成高低价
        daily_range = abs(np.random.normal(0, 0.01))  # 1%的日内波动
        high = price * (1 + daily_range)
        low = price * (1 - daily_range)
        
        closes.append(price)
        highs.append(high)
        lows.append(low)
        
        base_price = price
    
    # 转换为numpy数组
    closes = np.array(closes)
    highs = np.array(highs)
    lows = np.array(lows)
    
    print(f"📊 测试数据: {len(closes)}天价格数据")
    print(f"   价格范围: {closes.min():.2f} - {closes.max():.2f}")
    print(f"   最终价格: {closes[-1]:.2f}")
    
    # 调用移动止盈控制函数
    result = detector.calculate_moving_profit_control(highs, lows, closes)
    
    print(f"\n📈 移动止盈方案结果:")
    print(f"   ATR_14日: {result.get('ATR_14日', 0):.4f}")
    print(f"   ATR_百分比: {result.get('ATR_百分比', 0):.2f}%")
    print(f"   ATR_倍数: {result.get('ATR_倍数', 0):.2f}")
    print(f"   止损距离: {result.get('止损距离', 0):.2f}%")
    print(f"   初始止盈距离: {result.get('初始止盈距离', 0):.2f}%")
    print(f"   移动触发距离: {result.get('移动触发距离', 0):.2f}%")
    print(f"   当前价格: {result.get('当前价格', 0):.2f}")
    print(f"   波动率区间: {result.get('波动率区间', '未知')}")
    print(f"   计算模式: {result.get('计算模式', '未知')}")
    
    # 验证方案要求
    print(f"\n✅ 方案验证:")
    
    # 1. 验证风险收益对称
    止损距离 = result.get('止损距离', 0)
    初始止盈距离 = result.get('初始止盈距离', 0)
    对称性 = abs(止损距离 - 初始止盈距离) < 0.01
    print(f"   风险收益对称: {'✅' if 对称性 else '❌'} (止损{止损距离:.2f}% vs 止盈{初始止盈距离:.2f}%)")
    
    # 2. 验证ATR倍数
    ATR_倍数 = result.get('ATR_倍数', 0)
    期望倍数 = 14.0 / 3.0
    倍数正确 = abs(ATR_倍数 - 期望倍数) < 0.1
    print(f"   ATR倍数正确: {'✅' if 倍数正确 else '❌'} (期望{期望倍数:.2f}, 实际{ATR_倍数:.2f})")
    
    # 3. 验证移动触发距离
    移动触发距离 = result.get('移动触发距离', 0)
    触发距离正确 = abs(移动触发距离 - 初始止盈距离) < 0.01
    print(f"   移动触发距离: {'✅' if 触发距离正确 else '❌'} (应等于初始止盈距离)")
    
    # 4. 验证合理性检查
    距离合理 = 0.15 <= 止损距离 <= 5.0 and 0.15 <= 初始止盈距离 <= 5.0
    print(f"   距离合理性: {'✅' if 距离合理 else '❌'} (0.15%-5.0%范围内)")
    
    return result

def simulate_moving_profit_scenario():
    """模拟移动止盈场景"""
    print(f"\n🎯 模拟移动止盈场景")
    
    # 假设参数
    入场价格 = 100.0
    初始止盈距离 = 0.23  # 0.23%
    移动触发距离 = 0.23  # 0.23%
    
    # 计算关键价格点
    初始止盈价格 = 入场价格 * (1 + 初始止盈距离 / 100)
    移动触发单位 = 移动触发距离 / 100 * 入场价格
    
    print(f"📊 场景参数:")
    print(f"   入场价格: {入场价格:.2f}")
    print(f"   初始止盈距离: {初始止盈距离:.2f}%")
    print(f"   初始止盈价格: {初始止盈价格:.3f}")
    print(f"   移动触发单位: {移动触发单位:.3f}")
    
    # 模拟价格上涨过程
    价格序列 = [100.0, 100.1, 100.23, 100.3, 100.46, 100.5, 100.69, 100.8, 100.92]
    
    print(f"\n📈 价格上涨过程:")
    
    当前止盈线 = 0
    
    for i, 当前价格 in enumerate(价格序列):
        print(f"\n第{i+1}步: 价格 {当前价格:.2f}")
        
        if 当前价格 >= 初始止盈价格:
            # 移动止盈已启动
            price_gain_from_initial = 当前价格 - 初始止盈价格
            move_times = int(price_gain_from_initial / 移动触发单位)
            
            # 计算新的止盈线
            initial_stop_price = 入场价格 + (初始止盈距离 / 100 * 入场价格)
            new_trailing_stop_price = initial_stop_price + (move_times * 移动触发单位)
            
            # 止盈线只能向上移动
            if new_trailing_stop_price > 当前止盈线:
                当前止盈线 = new_trailing_stop_price
                print(f"   ✅ 移动止盈启动: 移动{move_times}次, 止盈线 {当前止盈线:.3f}")
            else:
                print(f"   ➡️ 止盈线保持: {当前止盈线:.3f} (未达到移动条件)")
                
            # 计算保护的利润
            if 当前止盈线 > 入场价格:
                保护利润 = (当前止盈线 - 入场价格) / 入场价格 * 100
                print(f"   🛡️ 保护利润: {保护利润:.2f}%")
        else:
            print(f"   ⏳ 等待启动: 需达到 {初始止盈价格:.3f}")
    
    print(f"\n🎯 最终结果:")
    print(f"   最高价格: {max(价格序列):.2f}")
    print(f"   最终止盈线: {当前止盈线:.3f}")
    if 当前止盈线 > 入场价格:
        最终保护利润 = (当前止盈线 - 入场价格) / 入场价格 * 100
        print(f"   最终保护利润: {最终保护利润:.2f}%")

if __name__ == "__main__":
    print("🚀 移动止盈方案测试开始")
    print("=" * 50)
    
    # 测试移动止盈控制函数
    result = test_moving_profit_control()
    
    # 模拟移动止盈场景
    simulate_moving_profit_scenario()
    
    print("\n" + "=" * 50)
    print("✅ 移动止盈方案测试完成")
