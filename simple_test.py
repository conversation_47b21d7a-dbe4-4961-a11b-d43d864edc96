#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的ATRMD测试
"""

import numpy as np
import talib

def calculate_true_range(highs, lows, closes):
    """
    计算真实波动范围 TR1
    TR1:=MAX(MAX(H-L,ABS(REF(C,1)-H)),ABS(REF(C,1)-L))
    """
    try:
        # 确保输入数据是numpy数组
        highs = np.asarray(highs, dtype=np.float64)
        lows = np.asarray(lows, dtype=np.float64)
        closes = np.asarray(closes, dtype=np.float64)
        
        data_len = len(closes)
        if data_len < 2:
            return np.zeros(data_len)
        
        # 1. H - L
        hl = highs - lows
        
        # 2. ABS(REF(C,1) - H) 和 ABS(REF(C,1) - L)
        hc = np.zeros(data_len, dtype=np.float64)
        lc = np.zeros(data_len, dtype=np.float64)
        
        # 第一个值设为HL[0]
        hc[0] = hl[0]
        lc[0] = hl[0]
        
        for i in range(1, data_len):
            hc[i] = abs(closes[i-1] - highs[i])   # ABS(REF(C,1) - H)
            lc[i] = abs(closes[i-1] - lows[i])    # ABS(REF(C,1) - L)
        
        # 3. TR1 = MAX(MAX(HL, HC), LC)
        TR1 = np.maximum(np.maximum(hl, hc), lc)
        
        return TR1
        
    except Exception as e:
        print(f"❌ TR计算失败: {e}")
        return np.zeros(len(closes))

def calculate_ATRMD(highs, lows, closes, N=11, M=27, P=35):
    """
    计算ATRMD指标
    """
    try:
        # 确保输入数据是numpy数组
        highs = np.asarray(highs, dtype=np.float64)
        lows = np.asarray(lows, dtype=np.float64)
        closes = np.asarray(closes, dtype=np.float64)

        if len(closes) < N + M + P:
            # 数据不足时返回中性值50
            return np.full(len(closes), 50.0)

        # 1. 计算真实波动范围 TR1
        TR1 = calculate_true_range(highs, lows, closes)

        # 2. ATR1 = MA(TR1, N)
        ATR1 = talib.SMA(TR1, timeperiod=N)
        ATR1 = np.nan_to_num(ATR1, nan=1e-8)

        # 避免除零错误
        closes_safe = np.where(closes == 0, 1e-8, closes)

        # 3. ATRMD = (ATR1/C) / SUM(ATR1/C, M)
        atr_ratio = ATR1 / closes_safe
        atr_ratio = np.nan_to_num(atr_ratio, nan=1e-8)
        
        atr_ratio_sum = talib.SUM(atr_ratio, timeperiod=M)
        atr_ratio_sum = np.where(atr_ratio_sum == 0, 1e-8, atr_ratio_sum)
        
        ATRMD = atr_ratio / atr_ratio_sum
        ATRMD = np.nan_to_num(ATRMD, nan=0.0)

        # 4. ATRPCT = ATRMD * 100
        ATRPCT = ATRMD * 100

        # 5. 0-100标准化
        MAXATR = talib.MAX(ATRPCT, timeperiod=P)
        MINATR = talib.MIN(ATRPCT, timeperiod=P)
        
        # 避免除零
        denominator = MAXATR - MINATR
        denominator = np.where(denominator == 0, 1e-8, denominator)
        
        ATRN = (ATRPCT - MINATR) / denominator * 100
        ATRN = np.nan_to_num(ATRN, nan=50.0)  # 默认中性值50
        
        # 确保值在0-100范围内
        ATRN = np.clip(ATRN, 0.0, 100.0)

        return ATRN

    except Exception as e:
        print(f"❌ ATRMD计算失败: {e}")
        return np.full(len(closes), 50.0)

def test_atrmd():
    """测试ATRMD指标计算"""
    print("🧪 开始测试ATRMD指标...")
    
    # 创建测试数据
    np.random.seed(42)
    n_bars = 100
    
    # 生成模拟价格数据
    base_price = 100
    price_changes = np.random.normal(0, 0.02, n_bars)
    closes = [base_price]
    
    for change in price_changes[1:]:
        new_price = closes[-1] * (1 + change)
        closes.append(max(new_price, 1))
    
    closes = np.array(closes)
    
    # 生成高低价
    highs = closes * (1 + np.random.uniform(0, 0.03, n_bars))
    lows = closes * (1 - np.random.uniform(0, 0.03, n_bars))
    
    # 确保高低价逻辑正确
    highs = np.maximum(highs, closes)
    lows = np.minimum(lows, closes)
    
    print(f"📊 测试数据: {n_bars}根K线")
    print(f"   价格范围: {lows.min():.2f} - {highs.max():.2f}")
    
    try:
        # 测试ATRMD计算
        atrmd = calculate_ATRMD(highs, lows, closes)
        
        print(f"📈 ATRMD计算结果:")
        print(f"   数组长度: {len(atrmd)}")
        print(f"   值范围: {atrmd.min():.2f} - {atrmd.max():.2f}")
        print(f"   最后5个值: {atrmd[-5:]}")
        
        # 检查结果的合理性
        checks = []
        checks.append(("输出长度", len(atrmd) == n_bars))
        checks.append(("值范围0-100", np.all((atrmd >= 0) & (atrmd <= 100))))
        checks.append(("无NaN值", not np.any(np.isnan(atrmd))))
        
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}: {result}")
        
        # 测试强趋势条件
        strong_trend = (atrmd[-1] > 80) or (len(atrmd) > 1 and atrmd[-2] > 80) or (len(atrmd) > 2 and atrmd[-3] > 80)
        print(f"🔍 强趋势条件: {strong_trend}")
        print(f"   当前值: {atrmd[-1]:.2f}")
        if len(atrmd) > 1:
            print(f"   前一值: {atrmd[-2]:.2f}")
        if len(atrmd) > 2:
            print(f"   前二值: {atrmd[-3]:.2f}")
            
        print("✅ ATRMD指标测试完成")
        return all(result for _, result in checks)
        
    except Exception as e:
        print(f"❌ ATRMD测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_atrmd()
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n💥 测试失败！")
