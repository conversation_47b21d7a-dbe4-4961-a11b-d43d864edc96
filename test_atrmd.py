#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ATRMD指标测试脚本
测试新实现的ATRMD指标是否正常工作
"""

import sys
import os
import numpy as np

# 添加框架目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '框架'))

import importlib.util
spec = importlib.util.spec_from_file_location("sixsk", os.path.join(os.path.dirname(__file__), '框架', '6sk线.py'))
sixsk = importlib.util.module_from_spec(spec)
spec.loader.exec_module(sixsk)
CMFBIASDivergenceDetector = sixsk.CMFBIASDivergenceDetector

def test_atrmd():
    """测试ATRMD指标计算"""
    print("🧪 开始测试ATRMD指标...")
    
    # 创建测试数据
    np.random.seed(42)  # 固定随机种子以便重现
    n_bars = 100
    
    # 生成模拟价格数据
    base_price = 100
    price_changes = np.random.normal(0, 0.02, n_bars)  # 2%的日波动
    closes = [base_price]
    
    for change in price_changes[1:]:
        new_price = closes[-1] * (1 + change)
        closes.append(max(new_price, 1))  # 确保价格为正
    
    closes = np.array(closes)
    
    # 生成高低价（基于收盘价）
    highs = closes * (1 + np.random.uniform(0, 0.03, n_bars))  # 高价比收盘价高0-3%
    lows = closes * (1 - np.random.uniform(0, 0.03, n_bars))   # 低价比收盘价低0-3%
    
    # 确保高低价逻辑正确
    highs = np.maximum(highs, closes)
    lows = np.minimum(lows, closes)
    
    print(f"📊 测试数据: {n_bars}根K线")
    print(f"   价格范围: {lows.min():.2f} - {highs.max():.2f}")
    print(f"   收盘价范围: {closes.min():.2f} - {closes.max():.2f}")
    
    try:
        # 创建检测器实例
        detector = CMFBIASDivergenceDetector(
            SKDJ_N=8, SKDJ_M=4,
            CMF_N=27, CMF_M=18, CMF_P=35,
            BIAS_N=18, BIAS_P=35,
            ATRMD_N=11, ATRMD_M=27, ATRMD_P=35,
            VAE_基础TR=1.8, VAE_初始止损=1.5, VAE_周期=17,
            固定止损=0.5
        )
        
        print("✅ 检测器创建成功")
        
        # 测试ATRMD计算
        atrmd = detector.calculate_ATRMD(highs, lows, closes)
        
        print(f"📈 ATRMD计算结果:")
        print(f"   数组长度: {len(atrmd)}")
        print(f"   值范围: {atrmd.min():.2f} - {atrmd.max():.2f}")
        print(f"   最后5个值: {atrmd[-5:]}")
        
        # 检查结果的合理性
        if len(atrmd) == n_bars:
            print("✅ 输出长度正确")
        else:
            print(f"❌ 输出长度错误: 期望{n_bars}, 实际{len(atrmd)}")
            
        if np.all((atrmd >= 0) & (atrmd <= 100)):
            print("✅ 值范围正确 (0-100)")
        else:
            print(f"❌ 值范围错误: 应在0-100之间")
            
        if not np.any(np.isnan(atrmd)):
            print("✅ 无NaN值")
        else:
            print(f"❌ 包含NaN值")
            
        # 测试强趋势条件
        strong_trend_condition = (atrmd[-1] > 80) or (len(atrmd) > 1 and atrmd[-2] > 80) or (len(atrmd) > 2 and atrmd[-3] > 80)
        print(f"🔍 强趋势条件测试: {strong_trend_condition}")
        print(f"   当前值: {atrmd[-1]:.2f}")
        if len(atrmd) > 1:
            print(f"   前一值: {atrmd[-2]:.2f}")
        if len(atrmd) > 2:
            print(f"   前二值: {atrmd[-3]:.2f}")
            
        print("✅ ATRMD指标测试完成")
        return True
        
    except Exception as e:
        print(f"❌ ATRMD测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_atrmd()
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
